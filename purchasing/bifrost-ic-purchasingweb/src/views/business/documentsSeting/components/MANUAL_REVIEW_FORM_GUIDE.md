# 人工审核结果表单组件使用指南

## 📋 组件概览

**组件名称**: `manualReviewForm.vue`  
**功能**: 为"人工审核结果"标签页提供表单输入功能  
**创建日期**: 2025-09-22

## 🎯 功能特性

### ✅ 表单字段
1. **审核意见字段**
   - 组件类型: `el-textarea`
   - 标签文字: "审核意见"
   - 占位符: "请输入审核意见"
   - 行数: 5行
   - 字数限制: 10-500字符
   - 显示字数统计
   - 必填验证

2. **审核结果字段**
   - 组件类型: `el-radio-group`
   - 标签文字: "审核结果"
   - 选项:
     - "审核通过" (值: "approved") - 绿色文字
     - "退回修改" (值: "rejected") - 红色文字
   - 必填验证

3. **操作按钮**
   - 保存按钮: `type="primary"`，支持加载状态
   - 重置按钮: 清空表单数据

### ✅ 验证规则
```javascript
formRules: {
  reviewComment: [
    { required: true, message: '请输入审核意见', trigger: 'blur' },
    { min: 10, message: '审核意见至少需要10个字符', trigger: 'blur' },
    { max: 500, message: '审核意见不能超过500个字符', trigger: 'blur' }
  ],
  reviewResult: [
    { required: true, message: '请选择审核结果', trigger: 'change' }
  ]
}
```

## 🔧 Props 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `initialData` | Object | `{ reviewComment: '', reviewResult: '' }` | 初始表单数据 |
| `showEmptyState` | Boolean | `false` | 是否显示空状态提示 |

## 📤 Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `save` | `formData` | 保存表单时触发，传递表单数据 |
| `save-success` | `formData` | 保存成功后触发 |
| `reset` | - | 重置表单时触发 |

## 🔍 Methods 方法

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| `validateForm()` | - | `Promise<boolean>` | 验证表单，返回验证结果 |
| `getFormData()` | - | `Object` | 获取当前表单数据 |
| `setFormData(data)` | `Object` | - | 设置表单数据 |
| `clearValidate()` | - | - | 清除表单验证状态 |

## 💻 使用示例

### 基础使用
```vue
<template>
  <manual-review-form
    :initial-data="formData"
    @save="handleSave"
    @save-success="handleSaveSuccess"
    @reset="handleReset"
  />
</template>

<script>
import ManualReviewForm from './manualReviewForm.vue'

export default {
  components: {
    ManualReviewForm
  },
  data() {
    return {
      formData: {
        reviewComment: '',
        reviewResult: ''
      }
    }
  },
  methods: {
    handleSave(formData) {
      console.log('保存数据:', formData);
      // 调用API保存数据
      this.$api.saveReviewResult(formData);
    },
    
    handleSaveSuccess(formData) {
      console.log('保存成功:', formData);
      // 执行保存成功后的逻辑
    },
    
    handleReset() {
      console.log('表单已重置');
      this.formData = {
        reviewComment: '',
        reviewResult: ''
      };
    }
  }
}
</script>
```

### 高级使用（带验证）
```vue
<template>
  <manual-review-form
    ref="reviewForm"
    :initial-data="formData"
    @save="handleSave"
  />
  <el-button @click="validateAndSubmit">验证并提交</el-button>
</template>

<script>
export default {
  methods: {
    async validateAndSubmit() {
      try {
        const isValid = await this.$refs.reviewForm.validateForm();
        if (isValid) {
          const formData = this.$refs.reviewForm.getFormData();
          await this.submitData(formData);
        }
      } catch (error) {
        this.$message.error('验证失败');
      }
    },
    
    async submitData(formData) {
      // 提交数据到服务器
      await this.$api.submitReview(formData);
      this.$message.success('提交成功');
    }
  }
}
</script>
```

## 🎨 样式特性

### 响应式设计
- **桌面端**: 最大宽度600px，居中显示
- **移动端**: 全宽显示，标签宽度调整为80px

### 视觉效果
- **审核结果选项**: 不同颜色区分（通过/拒绝）
- **表单验证**: 错误状态红色边框提示
- **加载状态**: 保存按钮loading效果
- **焦点状态**: 无障碍访问支持

### 自定义样式类
```scss
.manual-review-form {
  // 表单容器
}

.review-form {
  // 表单主体
}

.review-textarea {
  // 文本区域样式
}

.review-radio-group {
  // 单选按钮组样式
}

.form-actions {
  // 按钮区域样式
}
```

## 🧪 测试用例

### 功能测试
1. **表单验证测试**
   - 空值验证
   - 最小长度验证
   - 最大长度验证
   - 必选项验证

2. **数据绑定测试**
   - 初始数据加载
   - 数据双向绑定
   - 重置功能

3. **事件触发测试**
   - 保存事件
   - 重置事件
   - 成功回调

### 样式测试
1. **响应式测试**
   - 桌面端布局
   - 移动端适配

2. **交互测试**
   - 按钮状态
   - 加载效果
   - 验证提示

## 🔗 集成说明

### 在主组件中集成
```vue
<!-- configModule.vue -->
<div v-if="mainTab.key === 'manual-results'" class="tab-content manual-results-content">
  <manual-review-form
    :initial-data="manualReviewFormData"
    @save="handleSaveManualReview"
    @save-success="handleManualReviewSaveSuccess"
    @reset="handleResetManualReview"
  />
</div>
```

### 数据管理
```javascript
data() {
  return {
    manualReviewFormData: {
      reviewComment: '',
      reviewResult: ''
    }
  }
},

methods: {
  handleSaveManualReview(formData) {
    // 调用API保存审核结果
    this.$callApi('saveManualReviewResult', formData);
  },
  
  handleManualReviewSaveSuccess(formData) {
    // 保存成功后的处理逻辑
  },
  
  handleResetManualReview() {
    // 重置表单数据
    this.manualReviewFormData = {
      reviewComment: '',
      reviewResult: ''
    };
  }
}
```

## 📝 注意事项

1. **数据格式**: 确保传入的 `initialData` 包含正确的字段结构
2. **验证规则**: 可根据业务需求调整验证规则
3. **API集成**: 在实际项目中需要替换模拟的API调用
4. **权限控制**: 可根据用户权限控制表单的可编辑性
5. **数据持久化**: 考虑在用户离开页面前保存草稿

## 🚀 扩展建议

1. **添加更多字段**: 如审核人、审核时间等
2. **支持附件上传**: 允许上传审核相关文件
3. **历史记录**: 显示历史审核记录
4. **批量操作**: 支持批量审核功能
5. **审核流程**: 集成工作流引擎
