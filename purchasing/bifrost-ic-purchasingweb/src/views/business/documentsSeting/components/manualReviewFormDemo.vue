<template>
  <div class="manual-review-form-demo">
    <h2>人工审核结果表单 - 演示页面</h2>
    <div class="demo-container">
      <manual-review-form
        :initial-data="formData"
        :show-empty-state="showEmptyState"
        @save="handleSave"
        @save-success="handleSaveSuccess"
        @reset="handleReset"
        ref="reviewForm"
      />
      
      <!-- 演示控制面板 -->
      <div class="demo-controls">
        <h3>演示控制</h3>
        <el-button @click="loadSampleData" size="small">加载示例数据</el-button>
        <el-button @click="clearData" size="small">清空数据</el-button>
        <el-button @click="toggleEmptyState" size="small">
          {{ showEmptyState ? '隐藏' : '显示' }}空状态
        </el-button>
        <el-button @click="validateForm" size="small" type="primary">验证表单</el-button>
        
        <div class="data-display">
          <h4>当前表单数据：</h4>
          <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ManualReviewForm from './manualReviewForm.vue'

export default {
  name: 'ManualReviewFormDemo',
  components: {
    ManualReviewForm
  },
  data() {
    return {
      showEmptyState: false,
      formData: {
        reviewComment: '',
        reviewResult: ''
      },
      sampleData: {
        reviewComment: '经过详细审核，该文件内容完整，格式规范，符合相关要求。建议通过审核。',
        reviewResult: 'approved'
      }
    }
  },
  methods: {
    handleSave(formData) {
      console.log('保存表单数据:', formData);
      this.$message.info('正在保存审核结果...');
      
      // 模拟API调用
      setTimeout(() => {
        this.formData = { ...formData };
      }, 500);
    },
    
    handleSaveSuccess(formData) {
      console.log('保存成功:', formData);
      this.$message.success('审核结果保存成功！');
    },
    
    handleReset() {
      console.log('重置表单');
      this.formData = {
        reviewComment: '',
        reviewResult: ''
      };
      this.$message.info('表单已重置');
    },
    
    loadSampleData() {
      this.formData = { ...this.sampleData };
      this.$message.success('示例数据已加载');
    },
    
    clearData() {
      this.formData = {
        reviewComment: '',
        reviewResult: ''
      };
      this.$message.info('数据已清空');
    },
    
    toggleEmptyState() {
      this.showEmptyState = !this.showEmptyState;
    },
    
    async validateForm() {
      try {
        const isValid = await this.$refs.reviewForm.validateForm();
        if (isValid) {
          this.$message.success('表单验证通过');
        } else {
          this.$message.error('表单验证失败');
        }
      } catch (error) {
        this.$message.error('验证过程中发生错误');
      }
    }
  }
}
</script>

<style scoped lang="scss">
.manual-review-form-demo {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.demo-container {
  display: flex;
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

h2 {
  color: #303133;
  margin-bottom: 20px;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
}

.manual-review-form {
  flex: 1;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e4e7ed;
}

.demo-controls {
  width: 300px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e4e7ed;
  padding: 20px;
  height: fit-content;

  h3 {
    margin: 0 0 16px 0;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
  }

  h4 {
    margin: 16px 0 8px 0;
    color: #606266;
    font-size: 14px;
    font-weight: 500;
  }

  .el-button {
    width: 100%;
    margin-bottom: 8px;
  }

  .data-display {
    margin-top: 20px;
    padding-top: 16px;
    border-top: 1px solid #e4e7ed;

    pre {
      background-color: #f8f9fa;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      padding: 12px;
      font-size: 12px;
      color: #606266;
      white-space: pre-wrap;
      word-break: break-all;
      max-height: 200px;
      overflow-y: auto;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .demo-container {
    flex-direction: column;
  }

  .demo-controls {
    width: 100%;
    order: -1;
  }
}
</style>
