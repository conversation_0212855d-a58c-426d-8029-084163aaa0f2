<!--
 * @Description:
 * @Version: 2.0
 * @Autor: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-12-15 11:53:44
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-05-14 19:33:18
-->
<template>
  <el-dialog
    ref="fileDialog"
    :title="titleText"
    width="80%"
    class="dialog-tab-style"
    append-to-body
    :close-on-click-modal="false"
    :visible.sync="purfileVisible"
    fullscreen
    v-if="purfileVisible"
  >
    <produce-pur-fileInfo ref="purFileInfo" @visibleBtn="visibleBtn" />
    <div v-if="nodeStateName === '采购文件审核中'" style="text-align: center; padding: 10px 40px 0 0">
      <el-button type="primary" @click="handleAudit">审核</el-button>
      <el-button type="primary" @click="handleWithdraw">撤回</el-button>
    </div>
    <div v-if="isEdit" style="text-align: center; padding: 10px 40px 0 0">
      <el-button v-if="showBtn" type="primary" @click="save">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import ProducePurFileInfo from "./produce-pur-fileInfo";
export default {
  //编制采购文件
  name: "produce-pur-fileDialog",
  components: { ProducePurFileInfo },
  data() {
    return {
      purfileVisible: false,
      titleText: "",
      isEdit: true,
      loading: false,
      showBtn: true,
      nodeStateName: '', // 根据状态显示不同的按钮
      row: {},
    };
  },

  mounted() {},
  methods: {
    // 审核
    handleAudit() {
      this.purfileVisible = false
      this.$parent.handleProjectInfoAuditDialog(this.row.getRowData().ID)
    },
    // 撤回
    handleWithdraw() {
      const params = {
        id: this.row.getRowData().ID
      }
      this.$confirm('确定撤回选中的项目?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$callApiParams('withdraw', params, result => {
          this.$message({
            message: "撤回成功",
            type: "success",
          });
          this.purfileVisible = false;
        })
      })
    },
    show(row, text, isEdit, showEditBidFile, isMakeFile) {
      this.purfileVisible = true;
      this.titleText = text;
      this.isEdit = isEdit;
      this.row = row
      if (row.nodeStateName) {
        this.nodeStateName = row.nodeStateName
      }
      this.$nextTick(() => {
        this.$refs.purFileInfo.init(row, text, isEdit, showEditBidFile, isMakeFile);
      });
    },
    save() {
      this.$refs.purFileInfo.save(() => {
        this.$message({
          message: "保存成功",
          type: "success",
        });
        this.$parent.init();
        this.purfileVisible = false;
      });
    },
    visibleBtn(isShow = true) {
      this.showBtn = isShow
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
